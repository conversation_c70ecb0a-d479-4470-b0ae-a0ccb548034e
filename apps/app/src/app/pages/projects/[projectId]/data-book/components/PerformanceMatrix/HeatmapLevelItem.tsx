import { useState } from 'react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';

type HeatmapLevelItemProps = {
  title: string;
  className?: string;
  scoreRange: string;
  description: string;
};
export const HeatmapLevelItem: React.FC<HeatmapLevelItemProps> = ({ title, scoreRange, description, className }) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const [scorePopoverOpen, setScorePopoverOpen] = useState(false);

  return (
    <Popover open={scorePopoverOpen} onOpenChange={setScorePopoverOpen}>
      <Popover.Trigger
        onPointerEnter={() => isLargeScreen && setScorePopoverOpen(true)}
        onPointerLeave={() => isLargeScreen && setScorePopoverOpen(false)}
      >
        <Badge label={title} className={className} />
      </Popover.Trigger>
      <Popover.Content side="top">
        <div className="flex flex-col gap-1 text-sm leading-6 font-medium">
          <span>{title}</span>
          <span className="font-bold">{scoreRange}</span>
          <span className="font-normal">{description}</span>
        </div>
      </Popover.Content>
    </Popover>
  );
};
